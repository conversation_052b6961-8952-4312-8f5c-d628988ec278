#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// Patterns to remove (console statements and debug comments)
const DEBUG_PATTERNS = [
  // Console statements
  /console\.log\([^)]*\);?\s*\n?/g,
  /console\.warn\([^)]*\);?\s*\n?/g,
  /console\.error\([^)]*\);?\s*\n?/g,
  /console\.debug\([^)]*\);?\s*\n?/g,
  /console\.info\([^)]*\);?\s*\n?/g,
  /console\.trace\([^)]*\);?\s*\n?/g,
  
  // Multi-line console statements
  /console\.(log|warn|error|debug|info|trace)\(\s*[^)]*\n[^)]*\);?\s*\n?/g,
  
  // Debug comments with emojis
  /\/\/.*[🔍📸📦💾✅❌⚠️🧪🔄📊📷💿🎯🚀⭐🔧🎨📱💡🎛️♿🔐⏭️⏰📋📐🎨]/g,
  
  // Debug method calls
  /\.debugIndexedDBContents\(\);?\s*\n?/g,
  
  // Empty lines that were left after removing console statements
  /^\s*\n$/gm
];

// Files to exclude from processing
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /coverage/,
  /\.log$/,
  /\.lock$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /remove-debug-logs\.js$/,
  /TECHNICAL_ARCHITECTURE\.md$/,
  /README\.md$/,
  /\.md$/
];

// File extensions to process
const INCLUDE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

function shouldProcessFile(filePath) {
  // Check if file should be excluded
  if (EXCLUDE_PATTERNS.some(pattern => pattern.test(filePath))) {
    return false;
  }
  
  // Check if file has correct extension
  const ext = path.extname(filePath);
  return INCLUDE_EXTENSIONS.includes(ext);
}

function removeDebugLogs(content) {
  let cleaned = content;
  
  // Apply all debug patterns
  DEBUG_PATTERNS.forEach(pattern => {
    cleaned = cleaned.replace(pattern, '');
  });
  
  // Remove multiple consecutive empty lines
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // Remove trailing whitespace from lines
  cleaned = cleaned.replace(/[ \t]+$/gm, '');
  
  return cleaned;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const cleaned = removeDebugLogs(content);
    
    // Only write if content changed
    if (content !== cleaned) {
      fs.writeFileSync(filePath, cleaned, 'utf8');
      console.log(`✅ Cleaned: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  let processedCount = 0;
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        processedCount += processDirectory(fullPath);
      } else if (stat.isFile() && shouldProcessFile(fullPath)) {
        if (processFile(fullPath)) {
          processedCount++;
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dirPath}:`, error.message);
  }
  
  return processedCount;
}

// Main execution
console.log('🧹 Starting comprehensive debug log removal...');
console.log('📁 Processing src/ directory...');

const processedCount = processDirectory('./src');

console.log(`\n✨ Debug log removal complete!`);
console.log(`📊 Processed ${processedCount} files`);
console.log('🎯 All console.log, console.warn, console.error statements removed');
console.log('🧹 Debug comments and empty lines cleaned up');
