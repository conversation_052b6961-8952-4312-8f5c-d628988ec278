import React from "react";
import {
  <PERSON>sponsive<PERSON><PERSON>r,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  <PERSON>,
  LineChart,
  Line
} from "recharts";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardBody, CardHeader, Button, Tabs, Tab, Chip, Modal, ModalContent, ModalHeader, ModalBody, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Divider } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Trade } from "../../types/trade";
import { useAccountingMethod } from "../../context/AccountingMethodContext";
import { useGlobalFilter } from "../../context/GlobalFilterContext";
import { isTradeInGlobalFilter } from "../../utils/dateFilterUtils";
import { useTruePortfolioWithTrades } from "../../hooks/use-true-portfolio-with-trades";

export interface DrawdownDataPoint {
  month: string;
  drawdown: number;
  volatility: number;
  capital: number;
  plPercentage: number;
  maxDrawdown: number;
  recovery: number;
}

interface DrawdownCurveProps {
  trades: Trade[];
  className?: string;
}

export const DrawdownCurve: React.FC<DrawdownCurveProps> = ({ trades, className }) => {
  const { accountingMethod } = useAccountingMethod();
  const useCashBasis = accountingMethod === 'cash';
  const { filter: globalFilter } = useGlobalFilter();
  const [selectedView, setSelectedView] = React.useState<"drawdown" | "volatility">("drawdown");
  const [isDetailModalOpen, setIsDetailModalOpen] = React.useState(false);
  const [selectedPeriod, setSelectedPeriod] = React.useState("YTD");

  // Get portfolio data using the existing hook
  const { monthlyPortfolios } = useTruePortfolioWithTrades(trades);

  // Filter trades based on global filter and accounting method
  const filteredTrades = React.useMemo(() => {
    if (globalFilter.type === 'all') {
      return trades;
    }
    return trades.filter(trade => isTradeInGlobalFilter(trade, globalFilter, useCashBasis));
  }, [trades, globalFilter, useCashBasis]);

  // Calculate drawdown and volatility data
  const drawdownData = React.useMemo(() => {
    const processedData = monthlyPortfolios
      .map(monthData => ({
        month: `${monthData.month} ${monthData.year}`,
        capital: monthData.finalCapital,
        pl: monthData.pl,
        plPercentage: monthData.startingCapital !== 0 ? (monthData.pl / monthData.startingCapital) * 100 : 0
      }));

    if (processedData.length === 0) return [];

    let runningMax = processedData[0]?.capital || 0;
    let maxDrawdownSeen = 0;

    return processedData.map((d, index) => {
      // Update running maximum
      if (d.capital > runningMax) runningMax = d.capital;
      
      // Calculate current drawdown
      const drawdown = runningMax !== 0 ? ((runningMax - d.capital) / runningMax) * 100 : 0;
      
      // Track maximum drawdown seen so far
      if (drawdown > maxDrawdownSeen) maxDrawdownSeen = drawdown;
      
      // Calculate recovery (how close we are to new highs)
      const recovery = runningMax !== 0 ? (d.capital / runningMax) * 100 : 100;
      
      // Calculate rolling volatility (3-month window)
      let volatility = 0;
      if (index >= 2) {
        const window = processedData.slice(Math.max(0, index - 2), index + 1);
        const returns = window.map(w => w.plPercentage);
        const mean = returns.reduce((a, b) => a + b, 0) / returns.length;
        const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length;
        volatility = Math.sqrt(variance);
      }

      return {
        month: d.month,
        drawdown: -drawdown, // Negative for visual representation
        volatility,
        capital: d.capital,
        plPercentage: d.plPercentage,
        maxDrawdown: -maxDrawdownSeen,
        recovery
      };
    });
  }, [monthlyPortfolios]);

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (drawdownData.length === 0) return { maxDrawdown: 0, avgVolatility: 0, currentDrawdown: 0, recoveryTime: 0 };

    const maxDrawdown = Math.min(...drawdownData.map(d => d.drawdown));
    const avgVolatility = drawdownData.reduce((sum, d) => sum + d.volatility, 0) / drawdownData.length;
    const currentDrawdown = drawdownData[drawdownData.length - 1]?.drawdown || 0;
    
    // Calculate average recovery time (simplified)
    let recoveryPeriods = 0;
    let inDrawdown = false;
    let drawdownStart = 0;
    
    drawdownData.forEach((d, i) => {
      if (d.drawdown < -0.5 && !inDrawdown) {
        inDrawdown = true;
        drawdownStart = i;
      } else if (d.drawdown >= -0.1 && inDrawdown) {
        inDrawdown = false;
        recoveryPeriods += i - drawdownStart;
      }
    });

    return {
      maxDrawdown: Math.abs(maxDrawdown),
      avgVolatility,
      currentDrawdown: Math.abs(currentDrawdown),
      recoveryTime: recoveryPeriods
    };
  }, [drawdownData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-content1 border border-divider rounded-lg p-3 shadow-lg">
          <p className="font-medium text-sm mb-2">{label}</p>
          {selectedView === "drawdown" ? (
            <>
              <p className="text-danger text-sm">
                Drawdown: {Math.abs(data.drawdown).toFixed(2)}%
              </p>
              <p className="text-default-600 text-sm">
                Recovery: {data.recovery.toFixed(1)}%
              </p>
            </>
          ) : (
            <>
              <p className="text-warning text-sm">
                Volatility: {data.volatility.toFixed(2)}%
              </p>
              <p className="text-default-600 text-sm">
                P&L: {data.plPercentage >= 0 ? '+' : ''}{data.plPercentage.toFixed(2)}%
              </p>
            </>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-col gap-3">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-danger/10">
              <Icon icon="lucide:trending-down" className="text-danger text-sm" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Drawdown Analysis</h3>
              <p className="text-xs text-default-500">Risk and volatility metrics</p>
            </div>
          </div>
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={() => setIsDetailModalOpen(true)}
            className="text-default-400 hover:text-primary"
          >
            <Icon icon="lucide:maximize-2" className="w-4 h-4" />
          </Button>
        </div>

        {/* Toggle between Drawdown and Volatility */}
        <Tabs
          selectedKey={selectedView}
          onSelectionChange={(key) => setSelectedView(key as "drawdown" | "volatility")}
          size="sm"
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary-500",
            tab: "max-w-fit px-0 h-8",
            tabContent: "group-data-[selected=true]:text-primary-500"
          }}
        >
          <Tab
            key="drawdown"
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:trending-down" className="w-4 h-4" />
                <span>Drawdown</span>
              </div>
            }
          />
          <Tab
            key="volatility"
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:activity" className="w-4 h-4" />
                <span>Volatility</span>
              </div>
            }
          />
        </Tabs>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <div className="text-center">
            <p className="text-xs text-default-500">Max Drawdown</p>
            <p className="text-sm font-semibold text-danger">
              {summaryStats.maxDrawdown.toFixed(2)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-xs text-default-500">Current DD</p>
            <p className="text-sm font-semibold text-warning">
              {summaryStats.currentDrawdown.toFixed(2)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-xs text-default-500">Avg Volatility</p>
            <p className="text-sm font-semibold text-primary">
              {summaryStats.avgVolatility.toFixed(2)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-xs text-default-500">Recovery</p>
            <p className="text-sm font-semibold text-success">
              {summaryStats.recoveryTime} months
            </p>
          </div>
        </div>
      </CardHeader>
      <Divider />
      <CardBody>
        <div className="h-[280px]">
          <ResponsiveContainer width="100%" height="100%">
            <AnimatePresence mode="wait">
              {selectedView === "drawdown" ? (
                <AreaChart
                  data={drawdownData}
                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorDrawdown" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="hsl(var(--heroui-danger-500))" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="hsl(var(--heroui-danger-500))" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--heroui-divider))" />
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11 }}
                    dy={10}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11 }}
                    tickFormatter={(value) => `${Math.abs(value).toFixed(1)}%`}
                    domain={['dataMin', 0]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area 
                    type="monotone" 
                    dataKey="drawdown" 
                    name="Drawdown"
                    stroke="hsl(var(--heroui-danger))" 
                    fillOpacity={1}
                    fill="url(#colorDrawdown)" 
                    strokeWidth={2}
                    activeDot={{ r: 4, strokeWidth: 2 }}
                  />
                </AreaChart>
              ) : (
                <LineChart
                  data={drawdownData}
                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--heroui-divider))" />
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11 }}
                    dy={10}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11 }}
                    tickFormatter={(value) => `${value.toFixed(1)}%`}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="volatility" 
                    name="Volatility"
                    stroke="hsl(var(--heroui-warning))" 
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5, strokeWidth: 2 }}
                  />
                </LineChart>
              )}
            </AnimatePresence>
          </ResponsiveContainer>
        </div>
      </CardBody>

      {/* Detailed Modal */}
      <Modal
        isOpen={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
        size="4xl"
        scrollBehavior="inside"
        classNames={{
          base: "transform-gpu backdrop-blur-sm",
          wrapper: "transform-gpu",
          backdrop: "bg-black/40",
          closeButton: "text-foreground/60 hover:bg-white/10"
        }}
        backdrop="blur"
      >
        <ModalContent className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-2xl border border-gray-200 dark:border-gray-700 shadow-2xl max-h-[90vh]">
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1 border-b border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 px-4 py-3">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-danger/10">
                    <Icon icon="lucide:trending-down" className="text-danger text-sm" />
                  </div>
                  <div>
                    <span className="text-base font-semibold">Detailed Drawdown Analysis</span>
                    <p className="text-xs text-default-500 mt-0.5">
                      {useCashBasis ? 'Cash Basis' : 'Accrual Basis'} • Risk metrics breakdown
                    </p>
                  </div>
                </div>
              </ModalHeader>
              <ModalBody className="p-4">
                <div className="space-y-4">
                  {/* Enhanced Chart */}
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={drawdownData}
                        margin={{ top: 10, right: 30, left: 30, bottom: 30 }}
                      >
                        <defs>
                          <linearGradient id="colorDrawdownModal" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="hsl(var(--heroui-danger-500))" stopOpacity={0.4} />
                            <stop offset="95%" stopColor="hsl(var(--heroui-danger-500))" stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--heroui-divider))" />
                        <XAxis 
                          dataKey="month" 
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis 
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `${Math.abs(value).toFixed(1)}%`}
                          domain={['dataMin', 0]}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Area 
                          type="monotone" 
                          dataKey="drawdown" 
                          name="Drawdown %"
                          stroke="hsl(var(--heroui-danger))" 
                          fillOpacity={1}
                          fill="url(#colorDrawdownModal)" 
                          strokeWidth={2}
                          activeDot={{ r: 6, strokeWidth: 2 }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Detailed Statistics Table */}
                  <Table
                    aria-label="Drawdown analysis table"
                    classNames={{
                      wrapper: "max-h-[300px] border border-divider/30 rounded-lg overflow-hidden",
                      table: "border-collapse",
                      th: "bg-content1/50 text-sm font-medium text-default-600 border-b border-divider/30 px-3 py-2.5",
                      td: "py-2.5 px-3 text-sm border-b border-divider/20",
                      tr: "hover:bg-content1/20 transition-colors"
                    }}
                  >
                    <TableHeader>
                      <TableColumn>Period</TableColumn>
                      <TableColumn>Drawdown %</TableColumn>
                      <TableColumn>Volatility %</TableColumn>
                      <TableColumn>Recovery %</TableColumn>
                      <TableColumn>Portfolio Value</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {drawdownData.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.month}</TableCell>
                          <TableCell>
                            <span className="text-danger font-medium">
                              {Math.abs(item.drawdown).toFixed(2)}%
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="text-warning">
                              {item.volatility.toFixed(2)}%
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={item.recovery >= 95 ? "text-success" : "text-warning"}>
                              {item.recovery.toFixed(1)}%
                            </span>
                          </TableCell>
                          <TableCell>
                            ₹{item.capital.toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </Card>
  );
};
